package state

import (
	"testing"

	"gb-gateway/pkg/models"
)

func TestGetRealCameraDevicesWithPagination(t *testing.T) {
	manager := NewManager()

	// 创建测试设备
	devices := []models.Device{
		{
			GBID:       "34020000001320000001",
			Name:       "Camera 1",
			Status:     "ON",
			PlatformID: "platform1",
		},
		{
			GBID:       "34020000001320000002",
			Name:       "Camera 2",
			Status:     "ON",
			PlatformID: "platform1",
		},
		{
			GBID:       "34020000001320000003",
			Name:       "Camera 3",
			Status:     "ON",
			PlatformID: "platform1",
		},
		{
			GBID:       "34020000001320000004",
			Name:       "Camera 4",
			Status:     "ON",
			PlatformID: "platform1",
		},
		{
			GBID:       "34020000001320000005",
			Name:       "Camera 5",
			Status:     "ON",
			PlatformID: "platform1",
		},
	}

	// 更新设备类型并添加到管理器
	for _, device := range devices {
		device.UpdateDeviceType()
		manager.devices.Store(device.GBID, &device)
	}

	// 测试分页查询
	t.Run("First page with 2 items", func(t *testing.T) {
		cameras, total := manager.GetRealCameraDevicesWithPagination("platform1", 1, 2)

		if total != 5 {
			t.Errorf("Expected total 5, got %d", total)
		}

		if len(cameras) != 2 {
			t.Errorf("Expected 2 cameras, got %d", len(cameras))
		}

		// 验证排序（按GBID）
		if cameras[0].GBID != "34020000001320000001" {
			t.Errorf("Expected first camera GBID to be 34020000001320000001, got %s", cameras[0].GBID)
		}

		if cameras[1].GBID != "34020000001320000002" {
			t.Errorf("Expected second camera GBID to be 34020000001320000002, got %s", cameras[1].GBID)
		}
	})

	t.Run("Second page with 2 items", func(t *testing.T) {
		cameras, total := manager.GetRealCameraDevicesWithPagination("platform1", 2, 2)

		if total != 5 {
			t.Errorf("Expected total 5, got %d", total)
		}

		if len(cameras) != 2 {
			t.Errorf("Expected 2 cameras, got %d", len(cameras))
		}

		// 验证排序（按GBID）
		if cameras[0].GBID != "34020000001320000003" {
			t.Errorf("Expected first camera GBID to be 34020000001320000003, got %s", cameras[0].GBID)
		}

		if cameras[1].GBID != "34020000001320000004" {
			t.Errorf("Expected second camera GBID to be 34020000001320000004, got %s", cameras[1].GBID)
		}
	})

	t.Run("Last page with 1 item", func(t *testing.T) {
		cameras, total := manager.GetRealCameraDevicesWithPagination("platform1", 3, 2)

		if total != 5 {
			t.Errorf("Expected total 5, got %d", total)
		}

		if len(cameras) != 1 {
			t.Errorf("Expected 1 camera, got %d", len(cameras))
		}

		// 验证排序（按GBID）
		if cameras[0].GBID != "34020000001320000005" {
			t.Errorf("Expected camera GBID to be 34020000001320000005, got %s", cameras[0].GBID)
		}
	})

	t.Run("Page beyond range", func(t *testing.T) {
		cameras, total := manager.GetRealCameraDevicesWithPagination("platform1", 10, 2)

		if total != 5 {
			t.Errorf("Expected total 5, got %d", total)
		}

		if len(cameras) != 0 {
			t.Errorf("Expected 0 cameras, got %d", len(cameras))
		}
	})

	t.Run("Empty platform", func(t *testing.T) {
		cameras, total := manager.GetRealCameraDevicesWithPagination("nonexistent", 1, 2)

		if total != 0 {
			t.Errorf("Expected total 0, got %d", total)
		}

		if len(cameras) != 0 {
			t.Errorf("Expected 0 cameras, got %d", len(cameras))
		}
	})
}

func TestGetRealCameraDevicesOrdering(t *testing.T) {
	manager := NewManager()

	// 创建测试设备（故意不按顺序添加）
	devices := []models.Device{
		{
			GBID:       "34020000001320000003",
			Name:       "Camera 3",
			Status:     "ON",
			PlatformID: "platform1",
		},
		{
			GBID:       "34020000001320000001",
			Name:       "Camera 1",
			Status:     "ON",
			PlatformID: "platform1",
		},
		{
			GBID:       "34020000001320000002",
			Name:       "Camera 2",
			Status:     "ON",
			PlatformID: "platform1",
		},
	}

	// 更新设备类型并添加到管理器
	for _, device := range devices {
		device.UpdateDeviceType()
		manager.devices.Store(device.GBID, &device)
	}

	// 测试排序
	cameras := manager.GetRealCameraDevices("platform1")

	if len(cameras) != 3 {
		t.Errorf("Expected 3 cameras, got %d", len(cameras))
	}

	// 验证按GBID排序
	expectedOrder := []string{
		"34020000001320000001",
		"34020000001320000002",
		"34020000001320000003",
	}

	for i, expected := range expectedOrder {
		if cameras[i].GBID != expected {
			t.Errorf("Expected camera %d GBID to be %s, got %s", i, expected, cameras[i].GBID)
		}
	}
}
