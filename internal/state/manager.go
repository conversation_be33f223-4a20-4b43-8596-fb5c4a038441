package state

import (
	"fmt"
	"log/slog"
	"sort"
	"sync"
	"time"

	"gb-gateway/pkg/models"
)

// Manager manages application state
type Manager struct {
	platforms sync.Map // map[string]*models.Platform
	devices   sync.Map // map[string]*models.Device
	sessions  sync.Map // map[string]*models.StreamSession

	// Channels for async operations
	catalogResponses sync.Map // map[string]chan []models.Device
}

// NewManager creates a new state manager
func NewManager() *Manager {
	m := &Manager{}
	return m
}

// Platform operations
func (m *Manager) RegisterPlatform(platform *models.Platform) {
	platform.LastSeen = time.Now()
	m.platforms.Store(platform.ID, platform)
	slog.Info("Platform registered", "id", platform.ID, "uri", platform.SIPURI)
}

func (m *Manager) GetPlatform(id string) (*models.Platform, bool) {
	if value, ok := m.platforms.Load(id); ok {
		return value.(*models.Platform), true
	}
	return nil, false
}

func (m *Manager) UpdatePlatformLastSeen(id string) {
	if value, ok := m.platforms.Load(id); ok {
		platform := value.(*models.Platform)
		platform.LastSeen = time.Now()
		m.platforms.Store(id, platform)
	}
}

func (m *Manager) GetAllPlatforms() []*models.Platform {
	var platforms []*models.Platform
	m.platforms.Range(func(key, value any) bool {
		platforms = append(platforms, value.(*models.Platform))
		return true
	})
	return platforms
}

// SetPlatformForTest 仅用于测试，直接设置平台而不修改LastSeen
func (m *Manager) SetPlatformForTest(platform *models.Platform) {
	m.platforms.Store(platform.ID, platform)
}

// Device operations
func (m *Manager) UpdateDevices(platformID string, devices []models.Device) {
	for _, device := range devices {
		if device.GBID == "" {
			slog.Error("Invalid device", "device", device)
			continue
		}
		device.PlatformID = platformID
		// 更新设备类型和相关属性
		device.UpdateDeviceType()
		m.devices.Store(device.GBID, &device)
	}

	slog.Info("Devices updated", "platform_id", platformID, "count", len(devices))
}

func (m *Manager) GetDevice(gbID string) (*models.Device, bool) {
	if value, ok := m.devices.Load(gbID); ok {
		return value.(*models.Device), true
	}
	return nil, false
}

func (m *Manager) GetAllDevices() []models.Device {
	var devices []models.Device
	m.devices.Range(func(key, value any) bool {
		devices = append(devices, *value.(*models.Device))
		return true
	})
	return devices
}

// GetRealCameraDevices 获取真实的摄像头设备（过滤掉行政区划等）
func (m *Manager) GetRealCameraDevices(platformID string) []models.Device {
	var cameras []models.Device
	m.devices.Range(func(key, value any) bool {
		device := value.(*models.Device)
		// 只返回真实的摄像头设备
		if device.IsRealCamera() && (platformID == "" || device.PlatformID == platformID) {
			cameras = append(cameras, *device)
		}
		return true
	})
	// 按GBID排序，确保分页查询时顺序一致
	sort.Slice(cameras, func(i, j int) bool {
		return cameras[i].GBID < cameras[j].GBID
	})
	return cameras
}

// GetRealCameraDevicesWithPagination 获取真实的摄像头设备（支持分页）
func (m *Manager) GetRealCameraDevicesWithPagination(platformID string, page, pageSize int) ([]models.Device, int) {
	// 获取所有符合条件的设备
	allCameras := m.GetRealCameraDevices(platformID)
	total := len(allCameras)

	// 计算分页范围
	start := (page - 1) * pageSize
	end := start + pageSize

	// 边界检查
	if start >= total {
		return []models.Device{}, total
	}
	if end > total {
		end = total
	}

	// 返回分页结果
	return allCameras[start:end], total
}

// Session operations
func (m *Manager) CreateSession(session *models.StreamSession) {
	session.StartTime = time.Now()
	session.Status = "requesting"
	m.sessions.Store(session.SessionID, session)
	slog.Info("Session created", "session_id", session.SessionID, "gb_id", session.GBID)
}

func (m *Manager) GetSession(sessionID string) (*models.StreamSession, bool) {
	if value, ok := m.sessions.Load(sessionID); ok {
		return value.(*models.StreamSession), true
	}
	return nil, false
}

func (m *Manager) UpdateSessionStatus(sessionID, status string) {
	if value, ok := m.sessions.Load(sessionID); ok {
		session := value.(*models.StreamSession)
		session.Status = status
		m.sessions.Store(sessionID, session)
		slog.Info("Session status updated", "session_id", sessionID, "status", status)
	}
}

func (m *Manager) DeleteSession(sessionID string) {
	m.sessions.Delete(sessionID)
	slog.Info("Session deleted", "session_id", sessionID)
}

// Async catalog operations
func (m *Manager) CreateCatalogChannel(sn string) chan []models.Device {
	ch := make(chan []models.Device, 1)
	m.catalogResponses.Store(sn, ch)

	// Auto cleanup after timeout
	go func() {
		time.Sleep(30 * time.Second)
		m.catalogResponses.Delete(sn)
		close(ch)
	}()

	return ch
}

func (m *Manager) SendCatalogResponse(sn string, devices []models.Device) error {
	if value, ok := m.catalogResponses.Load(sn); ok {
		ch := value.(chan []models.Device)
		select {
		case ch <- devices:
			m.catalogResponses.Delete(sn)
			return nil
		default:
			return fmt.Errorf("catalog channel is full or closed")
		}
	}
	return fmt.Errorf("catalog request with SN %s not found", sn)
}

// CleanupExpiredDevices 清理过期设备
func (m *Manager) CleanupExpiredDevices(expireTimeout time.Duration) int {
	var expiredDevices []string
	cutoffTime := time.Now().Add(-expireTimeout)

	m.devices.Range(func(key, value any) bool {
		device := value.(*models.Device)
		// 只有LastUpdated不为零值且在截止时间之前的设备才被认为是过期的
		if !device.LastUpdated.IsZero() && device.LastUpdated.Before(cutoffTime) {
			expiredDevices = append(expiredDevices, device.GBID)
		}
		return true
	})

	// 删除过期设备
	for _, gbID := range expiredDevices {
		m.devices.Delete(gbID)
		slog.Info("Expired device removed", "gb_id", gbID)
	}

	if len(expiredDevices) > 0 {
		slog.Info("Device cleanup completed", "removed_count", len(expiredDevices))
	}

	return len(expiredDevices)
}
