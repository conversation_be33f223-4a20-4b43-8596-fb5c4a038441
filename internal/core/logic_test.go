package core

import (
	"fmt"
	"testing"
	"time"

	"gb-gateway/internal/state"
	"gb-gateway/pkg/models"
)

func TestGetDevicesWithPagination(t *testing.T) {
	// 创建状态管理器和逻辑层
	stateManager := state.NewManager()
	logic := NewLogic(stateManager, nil) // SIP服务器在这个测试中不需要

	// 注册一个平台
	platform := &models.Platform{
		ID:       "platform1",
		SIPURI:   "sip:platform1@*************:5060",
		LastSeen: time.Now(),
	}
	stateManager.RegisterPlatform(platform)

	// 创建测试设备
	devices := []models.Device{
		{
			GBID:       "34020000001320000001",
			Name:       "Camera 1",
			Status:     "ON",
			PlatformID: "platform1",
		},
		{
			GBID:       "34020000001320000002",
			Name:       "Camera 2",
			Status:     "ON",
			PlatformID: "platform1",
		},
		{
			GBID:       "34020000001320000003",
			Name:       "Camera 3",
			Status:     "ON",
			PlatformID: "platform1",
		},
	}

	// 更新设备到状态管理器
	stateManager.UpdateDevices("platform1", devices)

	t.Run("Valid pagination request", func(t *testing.T) {
		cameras, pagination, err := logic.GetDevicesWithPagination("platform1", 1, 2)
		if err != nil {
			t.Errorf("Unexpected error: %v", err)
		}

		if len(cameras) != 2 {
			t.Errorf("Expected 2 cameras, got %d", len(cameras))
		}

		if pagination.Page != 1 {
			t.Errorf("Expected page 1, got %d", pagination.Page)
		}

		if pagination.PageSize != 2 {
			t.Errorf("Expected page size 2, got %d", pagination.PageSize)
		}

		if pagination.Total != 3 {
			t.Errorf("Expected total 3, got %d", pagination.Total)
		}

		if pagination.TotalPages != 2 {
			t.Errorf("Expected total pages 2, got %d", pagination.TotalPages)
		}
	})

	t.Run("Second page", func(t *testing.T) {
		cameras, pagination, err := logic.GetDevicesWithPagination("platform1", 2, 2)
		if err != nil {
			t.Errorf("Unexpected error: %v", err)
		}

		if len(cameras) != 1 {
			t.Errorf("Expected 1 camera, got %d", len(cameras))
		}

		if pagination.Page != 2 {
			t.Errorf("Expected page 2, got %d", pagination.Page)
		}

		if pagination.Total != 3 {
			t.Errorf("Expected total 3, got %d", pagination.Total)
		}
	})

	t.Run("Empty platform ID uses first platform", func(t *testing.T) {
		cameras, pagination, err := logic.GetDevicesWithPagination("", 1, 10)
		if err != nil {
			t.Errorf("Unexpected error: %v", err)
		}

		if len(cameras) != 3 {
			t.Errorf("Expected 3 cameras, got %d", len(cameras))
		}

		if pagination.Total != 3 {
			t.Errorf("Expected total 3, got %d", pagination.Total)
		}
	})

	t.Run("Nonexistent platform", func(t *testing.T) {
		_, _, err := logic.GetDevicesWithPagination("nonexistent", 1, 10)

		if err == nil {
			t.Error("Expected error for nonexistent platform")
		}

		expectedError := "platform nonexistent not found"
		if err.Error() != expectedError {
			t.Errorf("Expected error '%s', got '%s'", expectedError, err.Error())
		}
	})

	t.Run("Inactive platform", func(t *testing.T) {
		// 创建一个过期的平台，直接存储到状态管理器中
		oldPlatform := &models.Platform{
			ID:       "old_platform",
			SIPURI:   "sip:old@*************:5060",
			LastSeen: time.Now().Add(-10 * time.Minute), // 10分钟前
		}
		// 直接存储，避免RegisterPlatform覆盖LastSeen
		stateManager.SetPlatformForTest(oldPlatform)

		_, _, err := logic.GetDevicesWithPagination("old_platform", 1, 10)

		if err == nil {
			t.Error("Expected error for inactive platform")
			return
		}

		expectedError := "platform old_platform not active"
		if err.Error() != expectedError {
			t.Errorf("Expected error '%s', got '%s'", expectedError, err.Error())
		}
	})
}

func TestPaginationCalculation(t *testing.T) {
	stateManager := state.NewManager()
	logic := NewLogic(stateManager, nil) // SIP服务器在这个测试中不需要

	// 注册平台
	platform := &models.Platform{
		ID:       "platform1",
		SIPURI:   "sip:platform1@*************:5060",
		LastSeen: time.Now(),
	}
	stateManager.RegisterPlatform(platform)

	// 创建10个设备
	devices := make([]models.Device, 10)
	for i := 0; i < 10; i++ {
		devices[i] = models.Device{
			GBID:       fmt.Sprintf("34020000001320000%03d", i+1), // 使用3位数字格式，确保20位
			Name:       fmt.Sprintf("Camera %d", i+1),
			Status:     "ON",
			PlatformID: "platform1",
		}
	}
	stateManager.UpdateDevices("platform1", devices)

	testCases := []struct {
		page       int
		pageSize   int
		expectLen  int
		totalPages int
	}{
		{1, 3, 3, 4}, // 第1页，每页3个，共4页
		{2, 3, 3, 4}, // 第2页，每页3个
		{3, 3, 3, 4}, // 第3页，每页3个
		{4, 3, 1, 4}, // 第4页，每页3个，只有1个
		{5, 3, 0, 4}, // 第5页，超出范围
		{1, 5, 5, 2}, // 第1页，每页5个，共2页
		{2, 5, 5, 2}, // 第2页，每页5个
		{3, 5, 0, 2}, // 第3页，超出范围
	}

	for _, tc := range testCases {
		t.Run(fmt.Sprintf("page_%d_size_%d", tc.page, tc.pageSize), func(t *testing.T) {
			cameras, pagination, err := logic.GetDevicesWithPagination("platform1", tc.page, tc.pageSize)
			if err != nil {
				t.Errorf("Unexpected error: %v", err)
			}

			if len(cameras) != tc.expectLen {
				t.Errorf("Expected %d cameras, got %d", tc.expectLen, len(cameras))
			}

			if pagination.TotalPages != tc.totalPages {
				t.Errorf("Expected %d total pages, got %d", tc.totalPages, pagination.TotalPages)
			}

			if pagination.Total != 10 {
				t.Errorf("Expected total 10, got %d", pagination.Total)
			}
		})
	}
}
